
def get_prompt(summary: str , theme: str ) -> str:
    return f"""
Tu es un assistant spécialisé dans la rédaction de newsletters professionnelles.

À partir du résumé suivant, rédige une newsletter structurée et engageante en français :

---
🧠 Résumé : {summary}
---

Règles de rédaction :
- Utilise un ton professionnel, clair et informatif.
- Commence par un titre accrocheur lié au thème : "{theme}".
- Organise la newsletter avec des sections si possible.
- Termine par une suggestion ou ouverture vers une suite (call to action ou réflexion).
- Aucune mention de résumé ou du modèle utilisé.

📧 Commence la rédaction maintenant :
"""
