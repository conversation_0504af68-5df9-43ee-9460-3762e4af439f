# Agent Intelligent pour la Génération Automatisée de Newsletters

## Description
Ce projet propose un workflow automatisé complet pour la génération de newsletters à partir de diverses sources d'information (sites web, flux RSS, réseaux sociaux...). Il s'appuie sur des technologies d'IA, de traitement du langage naturel (TALN) et des outils de scraping, de résumé automatique et de génération HTML.

---

## Architecture du Projet

### 1. **Backend (agent_intelligent)**
- **Orchestration du workflow** : Utilisation de LangGraph pour enchaîner les étapes (scraping → résumé → génération → révision optionnelle).
- **Modules principaux** :
  - `src/models/` : Intégration de modèles LLM (Llama3 via Groq, Gemini via Google).
  - `src/prompts/` : Prompts spécialisés pour chaque étape (résumé, génération, révision).
  - `src/config.py` : Configuration centralisée (API keys, paramètres, validation).
  - `src/langgraph/workflow.py` : Construction du pipeline automatisé.
  - `src/tools/` : Outils pour scraping (web, RSS, Twitter), résumé, génération, révision, prétraitement.
  - `src/interfaces/backend/api.py` : API FastAPI exposant les endpoints `/generate` (génération) et `/revise` (révision via feedback utilisateur).
- **Fonctionnement** :
  - Scraping intelligent de contenus (web, RSS, Twitter) selon un thème et une description.
  - Résumé automatique via Llama3 (Groq).
  - Génération de la newsletter via Gemini (Google).
  - Révision possible selon feedback utilisateur.
  - Prompts personnalisés pour chaque étape, garantissant la qualité rédactionnelle.

### 2. **Frontend (Next.js)**
- **Structure** :
  - `src/app/` : Pages principales (`page.tsx`), layout, styles globaux.
  - `src/components/` : Composants réutilisables (formulaire, affichage newsletter, feedback, bouton copie).
  - `public/` : Assets graphiques.
- **Fonctionnalités** :
  - Saisie du thème et de la description.
  - Affichage de la newsletter générée.
  - Formulaire de feedback pour révision.
  - Intégration avec l'API backend pour génération/révision en temps réel.

### 3. **Technologies clés**
- **Backend** : Python, FastAPI, LangChain, LangGraph, Llama3 (Groq), Gemini (Google), BeautifulSoup, feedparser, snscrape.
- **Frontend** : Next.js, React, TypeScript, CSS.
- **Interopérabilité** : API REST entre frontend et backend.

### 4. **Pipeline automatisé**
1. **Collecte** : Scraping multi-sources (web, RSS, Twitter).
2. **Prétraitement** : Nettoyage, normalisation, détection de langue.
3. **Résumé** : Modèle Llama3 via Groq.
4. **Génération** : Modèle Gemini via prompt structuré.
5. **Révision** : Sur demande, via prompt enrichi par le feedback utilisateur.
6. **Diffusion** : (À intégrer) vers plateformes emailing.

---

## Organisation des dossiers

```

  src/
    config.py              # Configuration centralisée
    models/                # Modèles LLM (Groq, Gemini)
    prompts/               # Prompts pour chaque étape
    langgraph/             # Orchestration du workflow
    tools/                 # Outils : scraping, résumé, génération, révision
    interfaces/
      backend/             # API FastAPI
      frontend/            # Application Next.js
```

---

## Configuration

### Fichier de configuration (`src/config.py`)
Le projet utilise un système de configuration centralisé qui gère :
- **API Keys** : Validation automatique des clés Groq et Gemini
- **Paramètres des modèles** : Configuration des modèles LLM
- **Configuration du scraping** : Timeouts, délais, nombre de résultats
- **Configuration de l'API** : Host, port, CORS
- **Validation** : Vérification automatique des clés API requises

### Variables d'environnement (`.env`)
Dans le fichier `.env` dans le répertoire 'configs'  remplissez les clés néccesaires:

```env
# Configuration des API Keys (OBLIGATOIRE)
# Obtenez votre clé Groq sur https://console.groq.com/
GROQ_API_KEY=your_groq_api_key_here

# Obtenez votre clé Gemini sur https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

#Obtenez votre clé OpenAI sur  https://platform.openai.com
OPENAI_API_KEY=your_openai_api_key_here

#Obtenez votre autre clé depuis le site qui convient
OTHER_API_KEY=your_other_api_key_here

# Configuration optionnelle du serveur
API_HOST=127.0.0.1
API_PORT=8000

# Configuration du scraping (optionnel)
MAX_SCRAPING_RESULTS=3
SCRAPING_DELAY=3
```

### Obtention des clés API

#### 1. Clé Groq (pour Llama3)
1. Allez sur [https://console.groq.com/](https://console.groq.com/)
2. Créez un compte ou connectez-vous
3. Générez une nouvelle clé API
4. Copiez la clé dans votre fichier `.env`

#### 2. Clé Gemini (pour Google)
1. Allez sur [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
2. Connectez-vous avec votre compte Google
3. Créez une nouvelle clé API
4. Copiez la clé dans votre fichier `.env`

#### 3.Clé OpenAI
1.Allez sur le site officiel :[ https://platform.openai.com]
2.Connectez-vous (ou créez un compte si nécessaire)
3.Accédez à la section API Keys :
- Cliquez sur votre profil en haut à droite
- Sélectionnez "View API Keys"
4.Créez une nouvelle clé :
- Cliquez sur "Create new secret key"
- Donnez-lui un nom explicite (ex: "MonProjetDev")
- Copiez la clé immédiatement dans votre fichier `.env` (elle ne sera plus affichée ensuite)

---

## Installation & Lancement

### **1. Backend (API FastAPI)**

**Prérequis** : Python 3.10+, clés API Groq et Gemini

```bash
# Installation des dépendances
pip install -r requirements.txt

# Configuration
# Créez le fichier .env avec vos clés API (voir section Configuration)

# Lancement depuis la racine du projet
uvicorn src.interfaces.backend.api:app --reload
```

L'API sera accessible sur [http://127.0.0.1:8000](http://127.0.0.1:8000)

### **2. Frontend (Next.js)**

```bash
cd src/interfaces/frontend
npm install
npm run dev
```

L'interface sera accessible sur [http://localhost:3000](http://localhost:3000)

---

## Endpoints API

### POST `/generate`
Génère une newsletter à partir d'un thème et d'une description.

**Body :**
```json
{
  "theme": "Intelligence Artificielle",
  "description": "Actualités récentes sur l'IA"
}
```

### POST `/revise`
Révise une newsletter existante selon un feedback utilisateur.

**Body :**
```json
{
  "feedback": "Rendre le ton plus professionnel"
}
```

### GET `/`
Vérification de l'état de l'API.

---

