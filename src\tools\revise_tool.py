from langchain.tools import tool
from src.tools.revise import revise_newsletter

@tool
def revise_newsletter_tool(input_data: str) -> str:
    """Révise une newsletter existante selon un feedback. Format attendu: 'newsletter|||feedback'"""
    # Séparer la newsletter et le feedback
    if '|||' in input_data:
        newsletter, feedback = input_data.split('|||', 1)
    else:
        newsletter = input_data
        feedback = "Améliorer le contenu"
    return revise_newsletter(newsletter, feedback)
