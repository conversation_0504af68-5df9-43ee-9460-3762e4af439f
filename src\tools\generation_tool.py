from langchain.tools import tool
from src.tools.nwesletter_generator import generate_newsletter_from_summary

@tool
def generate_newsletter(input_data: str) -> str:
    """Génère une newsletter professionnelle à partir d’un résumé et d’un thème."""
    # S<PERSON><PERSON>er le thème et le résumé si possible
    if ': ' in input_data:
        theme, summary = input_data.split(': ', 1)
    else:
        theme = "Newsletter"
        summary = input_data
    return generate_newsletter_from_summary(summary, theme)
