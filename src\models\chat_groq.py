from langchain.chat_models.base import BaseChatModel
from langchain.schema import AIMessage, HumanMessage, ChatMessage
from typing import List
import requests
import os

class ChatGroq(BaseChatModel):
    def __init__(self, model: str = "llama3-8b-8192", temperature: float = 0.7):
        self.api_key = os.getenv("GROQ_API_KEY")
        self.model = model
        self.temperature = temperature
        self.base_url = "https://api.groq.com/openai/v1/chat/completions"

    def _generate(self, messages: List[ChatMessage], stop=None):
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": [{"role": msg.role, "content": msg.content} for msg in messages],
            "temperature": self.temperature,
        }

        response = requests.post(self.base_url, headers=headers, json=payload)
        response.raise_for_status()
        content = response.json()["choices"][0]["message"]["content"]

        return AIMessage(content=content)

    @property
    def _llm_type(self) -> str:
        return "groq-chat"

    def _call(self, messages: List[ChatMessage], stop=None) -> str:
        return self._generate(messages).content
