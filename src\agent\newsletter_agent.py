from langchain.agents import initialize_agent, AgentType
from langchain.chat_models import ChatOpenAI  # ou autre LLM
from src.tools.scraping_tool import scrape_news
from src.tools.summary_tool import summarize_text
from src.tools.generation_tool import generate_newsletter
from src.tools.revise_tool import revise_newsletter_tool
from src.models.chat_groq import ChatGroq
from src.tools import mcp





# Liste des outils à donner à l'agent
tools = [scrape_news, summarize_text, generate_newsletter, revise_newsletter_tool]

# 🔧 LLM utilisé par l'agent (OpenAI ici, tu peux mettre Groq si tu veux)
llm = ChatGroq(model="llama3-8b-8192", temperature=0.7)

# 🧠 Initialisation de l'agent avec outils et LLM
agent = initialize_agent(
    tools=tools,
    llm=llm,
    agent=AgentType.OPENAI_FUNCTIONS,  # ou AgentType.ZERO_SHOT_REACT_DESCRIPTION
    verbose=True
)

def generate_newsletter_mcp(theme, description):
    """
    Orchestration complète selon le protocole MCP :
    1. Enrichissement du contexte
    2. Génération via LLM
    3. Validation du résultat
    """
    context = mcp.enrich_context(theme, description)
    newsletter_brute = mcp.generate_newsletter(context, llm)
    valid, result = mcp.validate_newsletter(newsletter_brute, context)
    if not valid:
        raise ValueError(f"Newsletter non valide : {result}")
    return result
