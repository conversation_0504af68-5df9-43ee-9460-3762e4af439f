import os
from dotenv import load_dotenv

# Charger les variables d'environnement depuis le fichier .env
load_dotenv()

class Config:
    """Configuration centralisée pour l'agent intelligent de newsletter"""
    
    # API Keys
    GROQ_API_KEY = os.getenv("GROQ_API_KEY")
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    
    # Configuration des modèles
    GROQ_MODEL = "llama3-8b-8192"
    GEMINI_MODEL = "gemini-2.0-flash"
    
    # Configuration du scraping
    MAX_SCRAPING_RESULTS = 3
    SCRAPING_TIMEOUT = 10
    SCRAPING_DELAY = 3  # D<PERSON>lai anti-ratelimit
    
    # Configuration du résumé
    MAX_SUMMARY_TOKENS = 150
    TEXT_CHUNK_SIZE = 1024
    TEXT_CHUNK_OVERLAP = 100
    
    # Configuration de l'API
    API_HOST = "127.0.0.1"
    API_PORT = 8000
    API_RELOAD = True
    
    # Configuration CORS
    CORS_ORIGINS = ["http://localhost:3000"]
    
    # Validation des clés API
    @classmethod
    def validate_api_keys(cls):
        """Valide que toutes les clés API nécessaires sont présentes"""
        missing_keys = []
        
        if not cls.GROQ_API_KEY:
            missing_keys.append("GROQ_API_KEY")
        
        if not cls.GEMINI_API_KEY:
            missing_keys.append("GEMINI_API_KEY")
        
        if missing_keys:
            raise RuntimeError(f"❌ Clés API manquantes: {', '.join(missing_keys)}")
        
        return True
    
    @classmethod
    def get_config_summary(cls):
        """Retourne un résumé de la configuration pour le debug"""
        return {
            "groq_model": cls.GROQ_MODEL,
            "gemini_model": cls.GEMINI_MODEL,
            "max_scraping_results": cls.MAX_SCRAPING_RESULTS,
            "api_host": cls.API_HOST,
            "api_port": cls.API_PORT,
            "cors_origins": cls.CORS_ORIGINS
        }

# Instance globale de configuration
config = Config() 