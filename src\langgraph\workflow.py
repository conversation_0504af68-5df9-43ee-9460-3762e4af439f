from langgraph.graph import StateGraph, END
from typing import TypedDict, List, Optional
from src.tools.scraping_tool import scrape_news
from src.tools.summary_tool import summarize_text
from src.tools.generation_tool import generate_newsletter
from src.tools.revise_tool import revise_newsletter_tool


# 🔧 Structure de données (état global du graphe)
class GraphState(TypedDict):
    theme: str
    description: str
    scraped: Optional[List[str]]
    summary: Optional[str]
    newsletter: Optional[str]
    feedback: Optional[str]
    revised: Optional[str]


# 🔹 Nœud 1 : scraping
def scraping_node(state: GraphState) -> GraphState:
    scraped = scrape_news.invoke({
        "theme": state["theme"],
        "description": state["description"]
    })
    return {**state, "scraped": scraped}


# 🔹 Nœud 2 : résumé
def summary_node(state: GraphState) -> GraphState:
    text = "\n".join(state["scraped"])
    summary = summarize_text.invoke({"text": text})
    return {**state, "summary": summary}


# 🔹 Nœud 3 : génération
def generation_node(state: GraphState) -> GraphState:
    newsletter = generate_newsletter.invoke({
        "summary": state["summary"],
        "theme": state["theme"]
    })
    return {**state, "newsletter": newsletter}


# 🔹 Nœud 4 (optionnel) : révision
def revise_node(state: GraphState) -> GraphState:
    revised = revise_newsletter_tool.invoke({
        "newsletter": state["newsletter"],
        "feedback": state["feedback"]
    })
    return {**state, "revised": revised}


# 🧠 Création du graphe
def build_newsletter_graph(with_revision: bool = True):
    builder = StateGraph(GraphState)

    builder.add_node("scrape", scraping_node)
    builder.add_node("summarize", summary_node)
    builder.add_node("generate", generation_node)
    builder.add_node("revise", revise_node)

    builder.set_entry_point("scrape")
    builder.add_edge("scrape", "summarize")
    builder.add_edge("summarize", "generate")

    # 🔁 Choix conditionnel : feedback présent → revise, sinon fin
    def route_after_generate(state: GraphState) -> str:
        return "revise" if state.get("feedback") else END

    builder.add_conditional_edges("generate", route_after_generate)
    builder.set_finish_point("revise")

    return builder.compile()

