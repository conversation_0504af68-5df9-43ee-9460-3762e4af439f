from langchain.tools import tool
from typing import List
from src.tools.scraping import scrape_content

@tool
def scrape_news(query: str) -> List[str]:
    """Scrape des actualités liées à une requête. Format attendu: 'theme: description'"""
    # Séparer le thème et la description si possible
    if ': ' in query:
        theme, description = query.split(': ', 1)
    else:
        theme = query
        description = ""
    return scrape_content(theme, description)
