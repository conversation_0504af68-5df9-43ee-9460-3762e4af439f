from src.tools.scraping_tool import scrape_news
from src.tools.summary_tool import summarize_text


def enrich_context(theme, description):
    """
    Enrichit le contexte à partir du thème et de la description fournis par l'utilisateur.
    Scrape des actualités, les résume, et ajoute les sources au contexte.
    """
    # Scraping actualités
    query = f"{theme}: {description}"
    news_items = scrape_news(query)
    sources = '\n'.join(news_items[:5]) if news_items else 'Aucune source trouvée.'
    # Résumé des actualités
    news_text = '\n'.join(news_items)
    resume = summarize_text(news_text) if news_items else 'Pas de résumé disponible.'
    context = (
        f"Thème: {theme}\n"
        f"Description: {description}\n"
        f"Résumé des actualités: {resume}\n"
        f"Sources utilisées:\n{sources}\n"
        f"Contexte enrichi: Utilise le résumé et les sources pour générer une newsletter pertinente."
    )
    return context


def generate_newsletter(context, llm):
    """
    Génère une newsletter brute à partir du contexte enrichi en utilisant le LLM fourni.
    """
    # Appel du LLM (doit avoir une méthode generate ou similaire)
    if hasattr(llm, 'generate'):
        return llm.generate(context)
    elif hasattr(llm, '__call__'):
        return llm(context)
    else:
        raise ValueError("Le LLM fourni n'a pas de méthode de génération compatible.")


def validate_newsletter(newsletter, context):
    """
    Valide la newsletter générée : cohérence, respect du contexte, etc.
    Retourne (True, newsletter) si valide, sinon (False, message d'erreur).
    """
    # Exemple de validation simple (à personnaliser)
    if not newsletter or len(newsletter) < 100:
        return False, "Newsletter trop courte ou vide."
    if not context.split('\n')[0].split(':')[1].strip().lower() in newsletter.lower():
        return False, "Le thème n'est pas retrouvé dans la newsletter."
    return True, newsletter 