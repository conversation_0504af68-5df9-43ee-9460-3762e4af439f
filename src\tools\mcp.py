from src.tools.scraping_tool import scrape_news
from src.tools.summary_tool import summarize_text


def enrich_context(theme, description):
    """
    Enrichit le contexte à partir du thème et de la description fournis par l'utilisateur.
    Scrape des actualités, les résume, et ajoute les sources au contexte.
    """
    # Scraping actualités
    query = f"{theme}: {description}"
    news_items = scrape_news(query)
    sources = '\n'.join(news_items[:5]) if news_items else 'Aucune source trouvée.'
    # Résumé des actualités
    news_text = '\n'.join(news_items)
    resume = summarize_text(news_text) if news_items else 'Pas de résumé disponible.'
    context = (
        f"Thème: {theme}\n"
        f"Description: {description}\n"
        f"Résumé des actualités: {resume}\n"
        f"Sources utilisées:\n{sources}\n"
        f"Contexte enrichi: Utilise le résumé et les sources pour générer une newsletter pertinente."
    )
    return context


def generate_newsletter(context, llm):
    """
    Génère une newsletter brute à partir du contexte enrichi en utilisant le LLM fourni.
    """
    from langchain.schema import HumanMessage

    # Créer un prompt pour la génération de newsletter
    prompt = f"""Tu es un assistant spécialisé dans la rédaction de newsletters professionnelles.

À partir du contexte suivant, rédige une newsletter structurée et engageante en français :

{context}

Règles de rédaction :
- Utilise un ton professionnel, clair et informatif.
- Commence par un titre accrocheur.
- Organise la newsletter avec des sections si possible.
- Termine par une suggestion ou ouverture vers une suite.
- Aucune mention de résumé ou du modèle utilisé.

📧 Commence la rédaction maintenant :"""

    try:
        # Utiliser la méthode invoke de LangChain
        if hasattr(llm, 'invoke'):
            response = llm.invoke([HumanMessage(content=prompt)])
            return response.content if hasattr(response, 'content') else str(response)
        # Fallback pour d'autres types de LLM
        elif hasattr(llm, '__call__'):
            return llm([HumanMessage(content=prompt)])
        else:
            raise ValueError("Le LLM fourni n'a pas de méthode de génération compatible.")
    except Exception as e:
        return f"Erreur lors de la génération : {str(e)}"


def validate_newsletter(newsletter, context):
    """
    Valide la newsletter générée : cohérence, respect du contexte, etc.
    Retourne (True, newsletter) si valide, sinon (False, message d'erreur).
    """
    # Exemple de validation simple (à personnaliser)
    if not newsletter or len(newsletter) < 100:
        return False, "Newsletter trop courte ou vide."
    if not context.split('\n')[0].split(':')[1].strip().lower() in newsletter.lower():
        return False, "Le thème n'est pas retrouvé dans la newsletter."
    return True, newsletter 