import os
from dotenv import load_dotenv
import google.generativeai as genai

# 🔐 Charger la clé depuis .env
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if not GEMINI_API_KEY:
    raise RuntimeError("❌ Clé API Gemini manquante. Ajoute-la dans .env comme GEMINI_API_KEY=...")

# 🔧 Configuration Gemini
genai.configure(api_key=GEMINI_API_KEY)

# 📦 Modèle exporté
gemini__model = genai.GenerativeModel("gemini-2.0-flash")



import os
from dotenv import load_dotenv

# 🔐 Charger toutes les clés depuis .env
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OTHER_API_KEY = os.getenv("OTHER_API_KEY")  # Pour Claude/Mistral/etc.

# 🔧 Initialisation du modèle
active_model = None
model_type = None

try:
    if GEMINI_API_KEY:
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        active_model = genai.GenerativeModel("gemini-2.0-flash")
        model_type = "gemini"
        print("✅ Modèle Gemini chargé")
    elif OPENAI_API_KEY:
        import openai
        active_model = openai.OpenAI(api_key=OPENAI_API_KEY)
        model_type = "openai"
        print("✅ Modèle OpenAI chargé")
    elif OTHER_API_KEY:
        # Exemple avec Anthropic (Claude)
        from anthropic import Anthropic  # pip install anthropic
        active_model = Anthropic(api_key=OTHER_API_KEY)
        model_type = "claude" #par exemple claude/mistral/etc
        print("✅ Modèle Claude chargé")
    
        raise RuntimeError("Aucune clé API valide trouvée")
except ImportError as e:
    print(f"❌ Bibliothèque manquante: {e}. Installe-la avec pip")
except Exception as e:
    print(f"❌ Erreur d'initialisation: {e}")

# 🛠️ Fonction unifiée pour interroger n'importe quel modèle
def ask_ai(prompt: str, model: str = None) -> str:
    """
    Pose une question au modèle AI actif
    
    Args:
        prompt: Question à poser
        model: Forcer un modèle spécifique (optionnel)
    
    Returns:
        Réponse textuelle
    """
    if not active_model:
        return "❌ Aucun modèle AI configuré"
    
    try:
        if model_type == "gemini" or model == "gemini":
            response = active_model.generate_content(prompt)
            return response.text
        elif model_type == "openai" or model == "openai":
            response = active_model.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}]
            )
            return response.choices[0].message.content
        elif model_type == "claude" or model == "claude":
            response = active_model.messages.create(
                model="claude-3-opus-20240229",
                max_tokens=1000,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text
        else:
            return "❌ Type de modèle non supporté"
    except Exception as e:
        return f"❌ Erreur lors de la requête: {str(e)}"

