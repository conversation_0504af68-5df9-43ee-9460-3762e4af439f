"use client";

import * as React from "react";
import { useState } from "react";
import axios from "axios";
import Form from "@/components/Form";
import NewsletterDisplay from "@/components/NewsletterDisplay";
import FeedbackForm from "@/components/FeedbackForm";
import CopyButton from "@/components/CopyButton";

export default function Home() {
  const [newsletter, setNewsletter] = useState("");
  const [revised, setRevised] = useState("");
  const [copied, setCopied] = useState(false);

  const generateNewsletter = async (theme: string, description: string) => {
    try {
      const res = await axios.post("http://localhost:8000/generate_mcp", {
        theme,
        description,
      });
      setNewsletter(res.data.newsletter);
      setRevised("");
      setCopied(false);
    } catch {
      alert("Erreur lors de la génération.");
    }
  };

  const reviseNewsletter = async (feedback: string) => {
    try {
      const res = await axios.post("http://localhost:8000/revise", {
        feedback,
      });
      setRevised(res.data.revised_newsletter);
      setNewsletter("");
      setCopied(false);
    } catch {
      alert("Erreur lors de la révision.");
    }
  };

  const content = revised || newsletter;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-700 via-blue-600 to-indigo-800 p-8 flex flex-col items-center">
      
      {/* 💡 ZONE 1 : Image + Form side by side */}
      <div className="flex flex-col md:flex-row bg-white rounded-2xl shadow-lg overflow-hidden w-full max-w-6xl">
        
        {/* Illustration à gauche */}
        <div className="w-full md:w-1/2 bg-gradient-to-b from-purple-800 to-blue-700 text-white flex flex-col justify-center items-center p-8">
          <img
            src="/nwesletter.jpg"
            alt="AI Illustration"
            className="w-64 h-64 md:w-80 md:h-80 border-4 border-white rounded-xl shadow-lg mb-4"
          />
          <h1 className="text-3xl font-bold text-center">Hello! 👋</h1>
          <p className="text-xl mt-1 text-center text-purple-100">
            Générez vos newsletters automatiquement avec l'IA ✨
          </p>
        </div>

        {/* Formulaire à droite */}
        <div className="w-full md:w-1/2 p-8">
          <h2 className="text-2xl font-semibold text-purple-800 text-center mb-6">
            Générateur de Newsletter
          </h2>
          <Form onGenerate={generateNewsletter} />
        </div>
      </div>

      {/* 📰 ZONE 2 : Newsletter + Feedback */}
      {content && (
        <div className="w-full max-w-6xl bg-white mt-8 p-6 rounded-xl shadow-md">
          <h3 className="text-xl font-bold text-gray-800 mb-4">Newsletter générée :</h3>

          <div className="bg-gray-50 border border-gray-300 rounded-md p-4 text-gray-800 whitespace-pre-wrap text-sm leading-relaxed">
            {content}
          </div>

          {!revised && (
            <div className="mt-4">
              <FeedbackForm onRevise={reviseNewsletter} />
            </div>
          )}

          <div className="mt-4">
            <CopyButton content={content} />
          </div>
        </div>
      )}
    </div>
  );
}
