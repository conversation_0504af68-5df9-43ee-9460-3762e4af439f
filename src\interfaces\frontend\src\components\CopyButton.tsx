"use client";

import { useState } from "react";

export default function CopyButton({ content }: { content: string }) {
  const [copied, setCopied] = useState(false);

  const copy = () => {
    navigator.clipboard.writeText(content);
    setCopied(true);
  };

  return (
    <button
      onClick={copy}
      className="mt-4 w-full bg-gray-800 text-white py-2 rounded-md hover:bg-gray-900 transition"
    >
      {copied ? "✅ Copié !" : "📋 Copier la Newsletter"}
    </button>
  );
}
